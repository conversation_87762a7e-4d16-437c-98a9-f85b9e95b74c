import { Column, Entity, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import BaseEntity from '../../../core/entities/base-entity';
import { Price } from 'src/modules/price/entities/price.entity';

@Entity('package')
export class Package extends BaseEntity {
  @ApiProperty({
    description: 'The name of the package',
    example: 'Premium Plan',
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Description of the package',
    example: 'Premium subscription plan with all features',
  })
  @Column()
  description: string;

  @ApiProperty({
    description: 'Image URL for the package',
    example: 'https://example.com/package-image.png',
  })
  @Column()
  image: string;

  @ApiProperty({
    description: 'Stripe Product ID',
    example: 'prod_1234567890',
  })
  // Stripe Product ID
  @Column({ unique: true })
  stripeProductId: string;

  @ApiProperty({
    description: 'Associated prices for this package',
    type: () => [Price],
  })
  // Một Product có thể có nhiều Price
  @OneToMany(() => Price, (price) => price.package)
  prices: Price[];
}
